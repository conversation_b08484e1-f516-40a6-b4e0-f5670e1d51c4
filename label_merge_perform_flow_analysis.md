# label_merge_perform 程序运行流程分析

## 概述

`label_merge_perform` 是一个标签融合接口，主要功能是对标签提取结果进行智能合并和去重处理。该接口集成了大模型请求、规则合并、MongoDB 数据存储、<PERSON>l<PERSON><PERSON> 向量检索等多种技术。

## 完整流程图

```mermaid
flowchart TD
    %% 主流程开始
    A["开始: label_merge_perform<br/>📁 api/routes/label_merge.py<br/>🔧 label_merge_perform()"] --> B["记录请求参数日志<br/>📁 utils/log_util.py<br/>🔧 LogUtil.log_json()"]
    B --> C{"检查缓存数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.coll().find_one()"}
    C -->|有缓存| D["返回缓存结果<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse()"]
    C -->|无缓存| E["从MongoDB获取历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()"]
    E --> F{"检查is_ai_extend参数<br/>📁 api/routes/label_merge.py<br/>🔧 request.is_ai_extend"}
    F -->|True且chain_structure为空| G["执行AI扩展<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_perform()"]
    F -->|False或已有结构| H["跳过AI扩展<br/>📁 api/routes/label_merge.py<br/>🔧 直接进入合并流程"]

    %% AI扩展子流程
    G --> G1["初始化变量<br/>📁 service/label_merge_service.py<br/>🔧 chain_structure = {}"]
    G1 --> G2["遍历product数组<br/>📁 service/label_merge_service.py<br/>🔧 for item in label_extract_info['product']"]
    G2 --> G3["AI扩展配对<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_pair()"]
    G3 --> G4["构建检索问题<br/>📁 service/label_merge_service.py<br/>🔧 question = f'请在{company}中的{product} 生成对应的产业链中的位置'"]

    %% 向量检索子流程
    G4 --> V1["知识库检索<br/>📁 service/kb_service.py<br/>🔧 KbService.search_knowledge_by_question()"]
    V1 --> V2["文本向量化<br/>📁 service/text_embed_service.py<br/>🔧 TextEmbedService.text_embedding()"]
    V2 --> V3["Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()"]
    V3 --> V4["获取Top-K文档<br/>📁 service/kb_service.py<br/>🔧 返回相关文档列表"]
    V4 --> G5["组织知识库内容<br/>📁 service/label_merge_service.py<br/>🔧 knowledge = '\\n\\n'.join(content_list)"]

    %% AI扩展大模型调用
    G5 --> G6["构建大模型提示词<br/>📁 configs/prompt_config.py<br/>🔧 PromptConfig.LABEL_MERGE_AI_EXTEND_SYSTEM_PROMPT"]
    G6 --> G7["大模型生成<br/>📁 service/llm_service.py<br/>🔧 Llm_Service.answer_question()"]
    G7 --> G8["解析JSON结果<br/>📁 utils/text_utils.py<br/>🔧 TextUtil.get_json_from_text()"]
    G8 --> G9["更新chain_structure<br/>📁 service/label_merge_service.py<br/>🔧 chain_structure[generate_key] = {}"]
    G9 --> G10{"是否还有product<br/>📁 service/label_merge_service.py<br/>🔧 循环判断"}
    G10 -->|是| G2
    G10 -->|否| I["树结构转换<br/>📁 utils/tree_utils.py<br/>🔧 TreeUtils.restore_tree()"]

    %% 合并流程
    H --> J["执行LLM合并<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()"]
    I --> J
    J --> J1["解析chain_structure<br/>📁 api/routes/label_merge.py<br/>🔧 提取节点、公司、产品标签"]
    J1 --> J2["创建异步任务<br/>📁 api/routes/label_merge.py<br/>🔧 tasks = [merge_task_multirunner_helper(...)]"]
    J2 --> J3["多任务运行器<br/>📁 api/routes/label_merge.py<br/>🔧 merge_task_multirunner_helper()"]

    %% LLM合并分支
    J3 --> L1["LLM标签合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.llm_merge()"]
    L1 --> L2["构建系统提示词<br/>📁 configs/prompt_config.py<br/>🔧 PromptConfig.LABEL_COMPOSE_SYSTEM_PROMPT"]
    L2 --> L3["大模型问答<br/>📁 service/llm_service.py<br/>🔧 Llm_Service.answer_question()"]
    L3 --> L4["解析合并结果<br/>📁 utils/text_utils.py<br/>🔧 TextUtil.get_json_from_text()"]
    L4 --> M1["合并任务结果<br/>📁 api/routes/label_merge.py<br/>🔧 asyncio.gather(*tasks)"]

    %% 规则合并判断
    M1 --> K{检查merge_type参数}
    K -->|Frequency| M["执行频率规则合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()"]
    K -->|Source| N["执行来源规则合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()"]

    %% 规则合并子流程
    M --> R1["标签替换接口<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 label_replace()"]
    N --> R1
    R1 --> R2["规则引擎处理<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 基于频率或来源的规则合并"]
    R2 --> O["规则合并处理<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()"]
    K -->|无需规则合并| O

    %% 结果保存
    O --> P["生成UUID<br/>📁 utils/uuid_util.py<br/>🔧 UuidUtil.get_uuid()"]
    P --> P1["保存结果到MongoDB<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()"]
    P1 --> Q["返回成功响应<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse()"]

    %% 异常处理
    A --> E1["异常捕获<br/>📁 api/routes/label_merge.py<br/>🔧 try-except块"]
    E1 --> E2["错误日志记录<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error()"]
    E2 --> E3["返回错误响应<br/>📁 entity/response_entity.py<br/>🔧 FalseResponse()"]

    %% 结束
    D --> Z[结束]
    Q --> Z
    E3 --> Z

    %% 样式定义
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style G fill:#fff3e0
    style V1 fill:#fff9c4
    style V3 fill:#e8f5e8
    style G7 fill:#f3e5f5
    style L3 fill:#f3e5f5
    style R1 fill:#fff3e0
    style E1 fill:#ffebee
    style E3 fill:#ffcdd2
```

## 流程图说明

上述完整流程图展示了 `label_merge_perform` 接口的完整执行过程，包含以下主要模块：

### 🔄 主流程模块
- **缓存检查**: 优先检查是否有缓存结果，提高响应速度
- **历史数据获取**: 从MongoDB获取标签提取的历史数据
- **参数判断**: 根据请求参数决定执行路径

### 🤖 AI扩展模块
- **产品遍历**: 对每个产品-公司对执行AI扩展
- **向量检索**: 使用Milvus进行语义相似度检索
- **大模型生成**: 基于检索到的知识生成产业链位置
- **结构更新**: 更新产业链结构数据

### 🔀 标签合并模块
- **LLM合并**: 使用大模型进行智能标签合并
- **规则合并**: 基于频率或来源的规则合并
- **异步处理**: 并行处理多种类型的标签

### 💾 数据持久化模块
- **结果存储**: 将处理结果保存到MongoDB
- **UUID生成**: 为每个结果生成唯一标识
- **日志记录**: 详细记录处理过程

### ⚠️ 异常处理模块
- **异常捕获**: 全局异常处理机制
- **错误日志**: 详细的错误信息记录
- **错误响应**: 统一的错误响应格式

## 核心接口说明

### 主要文件和接口

1. **api/routes/label_merge.py**
   - `label_merge_perform`: 主接口函数
   - `perform_common_merge_helper`: 通用合并助手
   - `merge_task_multirunner_helper`: 多任务运行助手

2. **service/label_merge_service.py**
   - `LabelMergeService`: 标签合并服务类
   - `ai_extend_perform`: AI扩展功能
   - `ai_extend_pair`: AI扩展配对
   - `llm_merge`: 大模型合并
   - `rule_merge`: 规则合并

3. **service/llm_service.py**
   - `Llm_Service.answer_question`: 大模型问答接口

4. **service/kb_service.py**
   - `KbService.search_knowledge_by_question`: 知识库检索接口

5. **utils/mongodb_util.py**
   - `MongodbUtil.insert_one`: 插入单条数据
   - `MongodbUtil.find_one`: 查询单条数据
   - `MongodbUtil.coll`: 获取集合对象

6. **utils/milvus_util.py**
   - `MilvusUtil.search_by_vector`: 向量检索
   - `MilvusUtil.collection_is_exists`: 检查集合存在性

### 关键功能模块

#### 1. 缓存机制
- 基于请求参数（mongodb_id, model, prompt, merge_type, is_ai_extend, is_cached）进行缓存
- 使用MongoDB存储缓存数据，提高响应速度

#### 2. AI扩展功能
- 通过产品和公司信息生成产业链位置
- 利用向量检索获取相关知识
- 大模型生成结构化的产业链信息

#### 3. 标签合并策略
- **LLM合并**: 使用大模型进行智能合并
- **规则合并**: 基于频率(Frequency)或来源(Source)的规则合并

#### 4. 数据持久化
- MongoDB存储处理结果和缓存数据
- Milvus存储向量化的知识库内容



## 详细流程说明

### 1. 请求参数验证
接口接收 `LabelMergeRequest` 对象，包含以下关键参数：
- `mongodb_id`: 标签提取返回的ID，用于获取历史数据
- `model`: 大模型名称（如 "qwen2.5-72B", "DeepSeek-R1-Distill-Qwen-32B"）
- `prompt`: 自定义提示词
- `merge_type`: 合并类型（"Frequency" 或 "Source"）
- `is_ai_extend`: 是否启用AI扩展功能
- `is_cached`: 是否使用缓存

### 2. 缓存查询机制
系统首先在 `LABEL_MERGE_PERFORM` 集合中查询是否存在相同参数的缓存：
```python
cached_data = MongodbUtil.coll(CollectionConfig.LABEL_MERGE_PERFORM).find_one({
    "raw_label_merge_data_id": request.mongodb_id,
    "model": request.model,
    "prompt": request.prompt,
    "merge_type": request.merge_type,
    "is_ai_extend": request.is_ai_extend,
    "is_cached": request.is_cached
}, sort=[('$natural', -1)])
```

### 3. AI扩展详细流程
当 `is_ai_extend=True` 且 `chain_structure` 为空时：

1. **遍历产品列表**: 对每个产品-公司对执行扩展
2. **向量检索**: 使用 `KbService` 在知识库中检索相关文档
3. **大模型生成**: 基于检索到的知识生成产业链位置
4. **结构更新**: 将生成的结果更新到 `chain_structure`
5. **树结构转换**: 使用 `TreeUtils.restore_tree` 转换为JSON格式

### 4. 标签合并策略详解

#### LLM合并流程：
1. **数据准备**: 提取节点、公司、产品标签
2. **并行处理**: 使用 `asyncio.gather` 并行处理三类标签
3. **大模型调用**: 每类标签独立调用大模型进行合并
4. **结果整合**: 将合并结果重新组织为完整结构

#### 规则合并流程：
1. **规则选择**: 根据 `merge_type` 选择合并规则
   - "Frequency": 基于频率的合并
   - "Source": 基于来源的合并
2. **接口调用**: 调用 `label_replace` 接口执行规则合并
3. **结果处理**: 处理合并结果并进行一致性检查

### 5. 数据持久化策略
- **结果存储**: 将最终合并结果存储到 `LABEL_MERGE` 集合
- **UUID生成**: 使用 `UuidUtil.get_uuid()` 生成唯一标识
- **日志记录**: 详细记录处理过程和结果

## 技术特点

1. **异步处理**: 大量使用async/await提高并发性能
2. **多任务并行**: 使用asyncio.gather并行处理多个合并任务
3. **智能缓存**: 基于参数组合的智能缓存机制
4. **向量检索**: 结合Milvus进行语义相似度检索
5. **大模型集成**: 深度集成LLM进行智能标签处理
6. **规则引擎**: 支持多种规则合并策略
7. **容错机制**: 完善的异常处理和日志记录
8. **数据一致性**: 多层次的数据验证和一致性检查



## 数据流转图

```mermaid
flowchart LR
    A[请求参数] --> B[缓存检查]
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[历史数据查询]
    D --> E[标签提取数据]
    E --> F{AI扩展判断}
    F -->|需要扩展| G[向量检索]
    G --> H[知识库文档]
    H --> I[大模型生成]
    I --> J[产业链结构]
    F -->|不需要扩展| K[LLM合并]
    J --> K
    K --> L[合并后标签]
    L --> M{规则合并判断}
    M -->|需要规则合并| N[规则引擎]
    N --> O[最终合并结果]
    M -->|不需要规则合并| O
    O --> P[MongoDB存储]
    P --> Q[响应结果]

    style A fill:#e3f2fd
    style G fill:#fff9c4
    style I fill:#f3e5f5
    style N fill:#fff3e0
    style P fill:#e8f5e8
    style Q fill:#e8f5e8
```

## 关键数据结构

### 输入数据结构
```json
{
  "mongodb_id": "标签提取ID",
  "model": "大模型名称",
  "prompt": "自定义提示词",
  "merge_type": "Frequency|Source",
  "is_ai_extend": true|false,
  "is_cached": true|false
}
```

### 标签提取历史数据结构
```json
{
  "chain_structure": {},
  "node_companies": {},
  "product": [
    {
      "product_abb": "产品简称",
      "company_abb": "公司简称"
    }
  ],
  "key_companies": [],
  "mongodb_id_node": {},
  "mongodb_id_info": {}
}
```

### AI扩展生成结构
```json
{
  "产业链名称": {
    "product": [
      {
        "product_abb": "产品简称",
        "company_abb": "公司简称"
      }
    ],
    "company": [],
    "source_list": [
      {
        "_id": "文档ID",
        "type": "研报",
        "title": "文档标题",
        "file_url": "文件URL"
      }
    ]
  }
}
```

### 最终输出结构
```json
{
  "_id": "融合结果ID",
  "chain_structure": "产业链结构",
  "chain_structure_json": "JSON格式产业链",
  "chain_structure_llm_merge": "LLM合并结果",
  "node_companies": "节点公司信息",
  "product": "产品信息",
  "mongodb_id_node": "节点映射",
  "mongodb_id_info": "信息映射",
  "request_param": "请求参数"
}
```

## 性能优化点

1. **缓存机制**: 避免重复计算，提高响应速度
2. **并行处理**: 多任务并行执行，提高处理效率
3. **向量检索**: 快速语义匹配，提高检索精度
4. **批量操作**: 减少数据库交互次数
5. **异步IO**: 非阻塞IO操作，提高系统吞吐量

## 总结

`label_merge_perform` 接口是一个复杂的标签融合系统，集成了多种先进技术：

1. **智能化**: 通过大模型实现智能标签合并
2. **知识驱动**: 基于向量检索的知识库增强
3. **多策略**: 支持LLM和规则两种合并策略
4. **高性能**: 异步并行处理，智能缓存机制
5. **可扩展**: 模块化设计，易于扩展新功能

该系统在产业链分析、标签管理等场景中具有重要应用价值。
