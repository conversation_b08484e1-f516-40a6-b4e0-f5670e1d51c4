# label_merge_perform 详细流程分析

## 概述

`label_merge_perform` 是一个标签融合接口，集成了大模型请求、规则合并、MongoDB 数据存储、Milvus 向量检索等多种技术。本文档详细展示了接口调用关系、入参出参、数据表操作等信息。

## 1. 主流程图

```mermaid
flowchart TD
    A["🚀 开始: label_merge_perform<br/>📁 api/routes/label_merge.py<br/>🔧 label_merge_perform()<br/>📥 入参: LabelMergeRequest<br/>📤 出参: SuccessResponse/FalseResponse"] --> B["📝 记录请求参数日志<br/>📁 utils/log_util.py<br/>🔧 LogUtil.log_json()<br/>📥 入参: request dict<br/>📤 出参: None"]
    
    B --> C{"🔍 检查缓存数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.coll().find_one()<br/>🗄️ 表: LABEL_MERGE_PERFORM<br/>📥 入参: 查询条件dict<br/>📤 出参: cached_data/None"}
    
    C -->|有缓存| D["✅ 返回缓存结果<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: cached_data<br/>📤 出参: SuccessResponse"]
    
    C -->|无缓存| E["📊 获取历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()<br/>🗄️ 表: LABEL_EXTRACT_PERFORM_HISTORY<br/>📥 入参: mongodb_id<br/>📤 出参: history dict"]
    
    E --> F{"🤖 检查AI扩展参数<br/>📁 api/routes/label_merge.py<br/>🔧 request.is_ai_extend判断<br/>📥 入参: is_ai_extend, chain_structure<br/>📤 出参: boolean"}
    
    F -->|需要AI扩展| G["🧠 执行AI扩展<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_perform()<br/>📥 入参: label_extract_info<br/>📤 出参: chain_structure, mongodb_id_node, mongodb_id_info"]
    
    F -->|跳过AI扩展| H["⏭️ 直接进入合并流程<br/>📁 api/routes/label_merge.py<br/>🔧 使用现有chain_structure<br/>📥 入参: 现有数据结构<br/>📤 出参: 无变化"]
    
    G --> I["🌳 树结构转换<br/>📁 utils/tree_utils.py<br/>🔧 TreeUtils.restore_tree()<br/>📥 入参: generate_dict<br/>📤 出参: chain_structure_json"]
    
    H --> J["🔄 执行LLM合并<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()<br/>📥 入参: label_extract_info, llm_merge<br/>📤 出参: label_merge_info"]
    I --> J
    
    J --> K{"📋 检查合并类型<br/>📁 api/routes/label_merge.py<br/>🔧 request.merge_type判断<br/>📥 入参: merge_type<br/>📤 出参: 执行路径"}
    
    K -->|Frequency| L["📊 频率规则合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()<br/>📥 入参: label_array, rule_type='num'<br/>📤 出参: rule_merge_result"]
    
    K -->|Source| M["📁 来源规则合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()<br/>📥 入参: label_array, rule_type='value'<br/>📤 出参: rule_merge_result"]
    
    L --> N["💾 保存结果<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: LABEL_MERGE<br/>📥 入参: label_merge_info<br/>📤 出参: insert_result"]
    M --> N
    K -->|无需规则合并| N
    
    N --> O["✅ 返回成功响应<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse()<br/>📥 入参: label_merge_info<br/>📤 出参: SuccessResponse"]
    
    %% 异常处理
    A --> E1["⚠️ 异常捕获<br/>📁 api/routes/label_merge.py<br/>🔧 try-except块<br/>📥 入参: Exception<br/>📤 出参: error_detail"]
    E1 --> E2["📝 错误日志<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error()<br/>📥 入参: error_message<br/>📤 出参: None"]
    E2 --> E3["❌ 错误响应<br/>📁 entity/response_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: error_data<br/>📤 出参: FalseResponse"]
    
    D --> Z[🏁 结束]
    O --> Z
    E3 --> Z
    
    style A fill:#e1f5fe
    style Z fill:#e8f5e8
    style G fill:#fff3e0
    style E1 fill:#ffebee
    style E3 fill:#ffcdd2
```

## 2. AI扩展子流程图

```mermaid
flowchart TD
    A["🧠 AI扩展开始<br/>📁 service/label_merge_service.py<br/>🔧 ai_extend_perform()<br/>📥 入参: label_extract_info<br/>📤 出参: chain_structure, mongodb_id_node, mongodb_id_info"] --> B["🔧 初始化变量<br/>📁 service/label_merge_service.py<br/>🔧 chain_structure = {}<br/>📥 入参: None<br/>📤 出参: 空字典"]
    
    B --> C["🔄 遍历产品数组<br/>📁 service/label_merge_service.py<br/>🔧 for item in product<br/>📥 入参: product数组<br/>📤 出参: 单个product item"]
    
    C --> D["🤝 AI扩展配对<br/>📁 service/label_merge_service.py<br/>🔧 ai_extend_pair()<br/>📥 入参: product_abb, company_abb<br/>📤 出参: generate_key, source_list"]
    
    D --> E["❓ 构建检索问题<br/>📁 service/label_merge_service.py<br/>🔧 构建question字符串<br/>📥 入参: product, company<br/>📤 出参: question string"]
    
    E --> F["🔍 知识库检索<br/>📁 service/kb_service.py<br/>🔧 search_knowledge_by_question()<br/>🗄️ 表: KNOWLEDGE_REPORT_ALL<br/>📥 入参: collection_name, question, limit_top_k=3<br/>📤 出参: doc_list"]
    
    F --> G["🧮 文本向量化<br/>📁 service/text_embed_service.py<br/>🔧 text_embedding()<br/>📥 入参: [question]<br/>📤 出参: embed_vector"]
    
    G --> H["🎯 Milvus向量检索<br/>📁 utils/milvus_util.py<br/>🔧 search_by_vector()<br/>🗄️ 向量库: Milvus<br/>📥 入参: collection_name, vector, limit_top_k<br/>📤 出参: search_results"]
    
    H --> I["📚 组织知识内容<br/>📁 service/label_merge_service.py<br/>🔧 join content_list<br/>📥 入参: content_list<br/>📤 出参: knowledge string"]
    
    I --> J["💭 构建提示词<br/>📁 configs/prompt_config.py<br/>🔧 LABEL_MERGE_AI_EXTEND_SYSTEM_PROMPT<br/>📥 入参: knowledge, product, company<br/>📤 出参: formatted_prompt"]
    
    J --> K["🤖 大模型生成<br/>📁 service/llm_service.py<br/>🔧 answer_question()<br/>📥 入参: messages, model, max_tokens=4096<br/>📤 出参: answer"]
    
    K --> L["📝 解析JSON结果<br/>📁 utils/text_utils.py<br/>🔧 get_json_from_text()<br/>📥 入参: answer<br/>📤 出参: prettify_json"]
    
    L --> M["🔄 更新结构<br/>📁 service/label_merge_service.py<br/>🔧 更新chain_structure<br/>📥 入参: generate_key, product_info<br/>📤 出参: 更新后的chain_structure"]
    
    M --> N{"🔁 循环判断<br/>📁 service/label_merge_service.py<br/>🔧 是否还有product<br/>📥 入参: 循环状态<br/>📤 出参: boolean"}
    
    N -->|是| C
    N -->|否| O["✅ 返回结果<br/>📁 service/label_merge_service.py<br/>🔧 return结果<br/>📥 入参: 完整数据<br/>📤 出参: chain_structure, mongodb_id_node, mongodb_id_info"]
    
    style A fill:#e1f5fe
    style F fill:#fff9c4
    style H fill:#e8f5e8
    style K fill:#f3e5f5
    style O fill:#e8f5e8
```

## 3. 标签合并子流程图

```mermaid
flowchart TD
    A["🔄 合并助手开始<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()<br/>📥 入参: label_extract_info, merge_func, merge_process<br/>📤 出参: label_merge_info"] --> B["📊 解析数据结构<br/>📁 api/routes/label_merge.py<br/>🔧 提取chain_structure<br/>📥 入参: label_extract_info<br/>📤 出参: chain_structure, company_abb_labels, product_label"]
    
    B --> C["⚡ 创建异步任务<br/>📁 api/routes/label_merge.py<br/>🔧 创建tasks数组<br/>📥 入参: merge_type, merge_array, merge_func<br/>📤 出参: tasks列表"]
    
    C --> D["🏃 多任务运行器<br/>📁 api/routes/label_merge.py<br/>🔧 merge_task_multirunner_helper()<br/>📥 入参: merge_type, merge_array, merge_func<br/>📤 出参: (merge_type, result)"]
    
    D --> E{"🤖 判断合并类型<br/>📁 api/routes/label_merge.py<br/>🔧 merge_process参数判断<br/>📥 入参: merge_process<br/>📤 出参: 执行路径"}
    
    E -->|LLM合并| F["🧠 LLM标签合并<br/>📁 service/label_merge_service.py<br/>🔧 llm_merge()<br/>📥 入参: label_array, is_industry<br/>📤 出参: merge_result"]
    
    E -->|规则合并| G["📋 规则标签合并<br/>📁 service/label_merge_service.py<br/>🔧 rule_merge()<br/>📥 入参: label_array, is_industry<br/>📤 出参: rule_merge_result"]
    
    F --> H["💭 构建系统提示词<br/>📁 configs/prompt_config.py<br/>🔧 LABEL_COMPOSE_SYSTEM_PROMPT<br/>📥 入参: prompt<br/>📤 出参: compose_system_prompt"]
    
    H --> I["🤖 大模型问答<br/>📁 service/llm_service.py<br/>🔧 answer_question()<br/>📥 入参: messages, model, max_tokens=4096<br/>📤 出参: answer"]
    
    I --> J["📝 解析合并结果<br/>📁 utils/text_utils.py<br/>🔧 get_json_from_text()<br/>📥 入参: answer<br/>📤 出参: prettify_json"]
    
    G --> K["🔄 标签替换接口<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 label_replace()<br/>📥 入参: input_labels, style<br/>📤 出参: response"]
    
    K --> L["⚙️ 规则引擎处理<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 基于频率或来源合并<br/>📥 入参: label_array, rule_type<br/>📤 出参: merged_labels"]
    
    J --> M["🔗 合并任务结果<br/>📁 api/routes/label_merge.py<br/>🔧 asyncio.gather(*tasks)<br/>📥 入参: tasks<br/>📤 出参: tasks_results"]
    L --> M
    
    M --> N["🏗️ 重组数据结构<br/>📁 api/routes/label_merge.py<br/>🔧 重组节点、公司、产品<br/>📥 入参: tasks_results<br/>📤 出参: 完整的label_merge_info"]
    
    N --> O["✅ 返回合并结果<br/>📁 api/routes/label_merge.py<br/>🔧 return label_merge_info<br/>📥 入参: 完整数据<br/>📤 出参: label_merge_info"]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#fff3e0
    style I fill:#f3e5f5
    style K fill:#fff3e0
    style O fill:#e8f5e8
```

## 4. 数据库操作流程图

```mermaid
flowchart TD
    A["💾 数据库操作开始<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil连接<br/>📥 入参: 连接配置<br/>📤 出参: 数据库连接"] --> B{"🔍 操作类型判断<br/>📁 api/routes/label_merge.py<br/>🔧 业务逻辑判断<br/>📥 入参: 业务场景<br/>📤 出参: 操作类型"}

    B -->|查询缓存| C["🔍 查询缓存数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.coll().find_one()<br/>🗄️ 表: LABEL_MERGE_PERFORM<br/>📥 入参: 查询条件{mongodb_id, model, prompt, merge_type, is_ai_extend, is_cached}<br/>📤 出参: cached_data/None"]

    B -->|查询历史| D["📊 查询历史数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()<br/>🗄️ 表: LABEL_EXTRACT_PERFORM_HISTORY<br/>📥 入参: mongodb_id<br/>📤 出参: history_document"]

    B -->|保存结果| E["💾 插入新数据<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ 表: LABEL_MERGE<br/>📥 入参: label_merge_info<br/>📤 出参: insert_result"]

    C --> F["✅ 缓存命中处理<br/>📁 api/routes/label_merge.py<br/>🔧 检查缓存条件匹配<br/>📥 入参: 查询参数组合<br/>📤 出参: 匹配结果"]

    D --> G["📋 历史数据解析<br/>📁 api/routes/label_merge.py<br/>🔧 解析数据结构<br/>📥 入参: history_document<br/>📤 出参: chain_structure, product, key_companies等"]

    E --> H["🆔 UUID生成<br/>📁 utils/uuid_util.py<br/>🔧 UuidUtil.get_uuid()<br/>📥 入参: None<br/>📤 出参: unique_id"]

    F --> I{"📊 缓存结果判断<br/>📁 api/routes/label_merge.py<br/>🔧 cached_data存在性检查<br/>📥 入参: cached_data<br/>📤 出参: boolean"}

    I -->|存在| J["✅ 返回缓存数据<br/>📁 entity/response_entity.py<br/>🔧 SuccessResponse(data=cached_data)<br/>📥 入参: cached_data<br/>📤 出参: SuccessResponse"]

    I -->|不存在| K["⏭️ 继续处理流程<br/>📁 api/routes/label_merge.py<br/>🔧 执行标签合并逻辑<br/>📥 入参: 请求参数<br/>📤 出参: 处理结果"]

    G --> L["🔧 数据结构提取<br/>📁 api/routes/label_merge.py<br/>🔧 提取各类标签数据<br/>📥 入参: 完整历史数据<br/>📤 出参: 结构化数据"]

    H --> M["📝 操作日志记录<br/>📁 utils/log_util.py<br/>🔧 LogUtil.log_json()<br/>📥 入参: 操作信息<br/>📤 出参: None"]

    style A fill:#e1f5fe
    style C fill:#e3f2fd
    style D fill:#e3f2fd
    style E fill:#e3f2fd
    style J fill:#e8f5e8
    style K fill:#fff3e0
```

## 5. 异常处理流程图

```mermaid
flowchart TD
    A["⚠️ 异常发生<br/>📁 api/routes/label_merge.py<br/>🔧 try-except块捕获<br/>📥 入参: Exception对象<br/>📤 出参: 异常信息"] --> B{"🔍 异常类型判断<br/>📁 api/routes/label_merge.py<br/>🔧 Exception类型分析<br/>📥 入参: Exception类型<br/>📤 出参: 异常分类"}

    B -->|JSON解析异常| C["📝 JSON解析错误<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error('json解析异常')<br/>📥 入参: 解析错误详情<br/>📤 出参: None"]

    B -->|数据库连接异常| D["💾 数据库连接错误<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error('数据库异常')<br/>📥 入参: 数据库错误信息<br/>📤 出参: None"]

    B -->|大模型调用异常| E["🤖 大模型调用错误<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error('LLM调用异常')<br/>📥 入参: LLM错误信息<br/>📤 出参: None"]

    B -->|向量检索异常| F["🎯 向量检索错误<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error('Milvus异常')<br/>📥 入参: Milvus错误信息<br/>📤 出参: None"]

    B -->|其他异常| G["❓ 通用异常处理<br/>📁 utils/log_util.py<br/>🔧 LogUtil.error(str(e))<br/>📥 入参: 通用异常信息<br/>📤 出参: None"]

    C --> H["❌ 返回错误响应<br/>📁 entity/response_entity.py<br/>🔧 FalseResponse()<br/>📥 入参: error_data<br/>📤 出参: FalseResponse"]
    D --> H
    E --> H
    F --> H
    G --> H

    H --> I["📋 包含错误详情<br/>📁 api/routes/label_merge.py<br/>🔧 data = {'error': detail}<br/>📥 入参: 错误详情字符串<br/>📤 出参: 错误数据字典"]

    I --> J["🏁 结束处理<br/>📁 api/routes/label_merge.py<br/>🔧 return FalseResponse(data=data)<br/>📥 入参: 错误响应数据<br/>📤 出参: FalseResponse"]

    style A fill:#ffebee
    style H fill:#ffcdd2
    style J fill:#e8f5e8
```

## 6. 接口调用关系总览

```mermaid
graph TD
    A["🚀 label_merge_perform<br/>📁 api/routes/label_merge.py<br/>🔧 主接口函数<br/>📥 LabelMergeRequest<br/>📤 SuccessResponse/FalseResponse"] --> B["🔍 缓存查询<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.coll().find_one()<br/>🗄️ LABEL_MERGE_PERFORM<br/>📥 查询条件dict<br/>📤 cached_data/None"]

    A --> C["📊 历史数据查询<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.query_doc_by_id()<br/>🗄️ LABEL_EXTRACT_PERFORM_HISTORY<br/>📥 mongodb_id<br/>📤 history_document"]

    A --> D["🧠 AI扩展服务<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_perform()<br/>📥 label_extract_info<br/>📤 chain_structure, mongodb_id_node, mongodb_id_info"]

    A --> E["🔄 通用合并助手<br/>📁 api/routes/label_merge.py<br/>🔧 perform_common_merge_helper()<br/>📥 label_extract_info, merge_func, merge_process<br/>📤 label_merge_info"]

    A --> F["💾 结果存储<br/>📁 utils/mongodb_util.py<br/>🔧 MongodbUtil.insert_one()<br/>🗄️ LABEL_MERGE<br/>📥 label_merge_info<br/>📤 insert_result"]

    D --> G["🤝 AI扩展配对<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.ai_extend_pair()<br/>📥 product_abb, company_abb<br/>📤 generate_key, source_list"]

    G --> H["🔍 知识库检索<br/>📁 service/kb_service.py<br/>🔧 KbService.search_knowledge_by_question()<br/>🗄️ KNOWLEDGE_REPORT_ALL<br/>📥 collection_name, question, limit_top_k<br/>📤 doc_list"]

    G --> I["🤖 大模型问答<br/>📁 service/llm_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 messages, model, max_tokens<br/>📤 answer"]

    H --> J["🧮 文本向量化<br/>📁 service/text_embed_service.py<br/>🔧 TextEmbedService.text_embedding()<br/>📥 [question]<br/>📤 embed_vector"]

    H --> K["🎯 向量检索<br/>📁 utils/milvus_util.py<br/>🔧 MilvusUtil.search_by_vector()<br/>🗄️ Milvus向量库<br/>📥 collection_name, vector, limit_top_k<br/>📤 search_results"]

    E --> L["🏃 多任务运行器<br/>📁 api/routes/label_merge.py<br/>🔧 merge_task_multirunner_helper()<br/>📥 merge_type, merge_array, merge_func<br/>📤 (merge_type, result)"]

    L --> M["🧠 LLM标签合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.llm_merge()<br/>📥 label_array, is_industry<br/>📤 merge_result"]

    L --> N["📋 规则标签合并<br/>📁 service/label_merge_service.py<br/>🔧 LabelMergeService.rule_merge()<br/>📥 label_array, is_industry<br/>📤 rule_merge_result"]

    M --> O["🤖 大模型调用<br/>📁 service/llm_service.py<br/>🔧 Llm_Service.answer_question()<br/>📥 messages, model, max_tokens<br/>📤 answer"]

    N --> P["🔄 标签替换接口<br/>📁 api/routes/company_full_name_supplement.py<br/>🔧 label_replace()<br/>📥 input_labels, style<br/>📤 response"]

    A --> Q["🌳 树结构转换<br/>📁 utils/tree_utils.py<br/>🔧 TreeUtils.restore_tree()<br/>📥 generate_dict<br/>📤 chain_structure_json"]

    A --> R["🆔 UUID生成<br/>📁 utils/uuid_util.py<br/>🔧 UuidUtil.get_uuid()<br/>📥 None<br/>📤 unique_id"]

    A --> S["📝 日志记录<br/>📁 utils/log_util.py<br/>🔧 LogUtil.log_json()<br/>📥 log_data<br/>📤 None"]

    style A fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style H fill:#fff9c4
    style I fill:#f3e5f5
    style O fill:#f3e5f5
```

## 7. 详细接口调用信息

### 7.1 label_merge_perform 主接口
**文件位置**: `api/routes/label_merge.py`
**方法名**: `label_merge_perform()`

**调用的其他接口**:
1. `LogUtil.log_json()` - 日志记录
2. `MongodbUtil.coll().find_one()` - 缓存查询
3. `MongodbUtil.query_doc_by_id()` - 历史数据查询
4. `LabelMergeService.ai_extend_perform()` - AI扩展
5. `perform_common_merge_helper()` - 标签合并
6. `TreeUtils.restore_tree()` - 树结构转换
7. `UuidUtil.get_uuid()` - UUID生成
8. `MongodbUtil.insert_one()` - 结果保存

**详细入参 (LabelMergeRequest)**:
```python
{
    "mongodb_id": str,           # 标签提取返回的MongoDB文档ID
    "model": str,               # 大模型名称，如"qwen2.5-72B"
    "prompt": str,              # 自定义提示词内容
    "merge_type": str,          # 合并类型："Frequency"或"Source"
    "is_ai_extend": bool,       # 是否启用AI扩展功能
    "is_cached": bool           # 是否使用缓存机制
}
```

**详细出参**:
```python
# 成功响应 (SuccessResponse)
{
    "code": int,                # 响应状态码，200表示成功
    "message": str,             # 响应消息
    "data": {                   # 标签融合结果数据
        "_id": str,             # 融合结果唯一标识
        "chain_structure": dict, # 产业链结构数据
        "chain_structure_json": list, # JSON格式产业链
        "chain_structure_llm_merge": dict, # LLM合并结果
        "node_companies": dict,  # 节点公司映射
        "product": list,        # 产品信息列表
        "mongodb_id_node": dict, # MongoDB节点ID映射
        "mongodb_id_info": dict, # MongoDB信息映射
        "request_param": dict   # 请求参数记录
    }
}

# 错误响应 (FalseResponse)
{
    "code": int,                # 错误状态码
    "message": str,             # 错误消息
    "data": {
        "error": str            # 具体错误详情
    }
}
```

**查询的数据表**:
1. `LABEL_MERGE_PERFORM` - 缓存查询
2. `LABEL_EXTRACT_PERFORM_HISTORY` - 历史数据查询
3. `LABEL_MERGE` - 结果保存

### 7.2 ai_extend_perform 接口
**文件位置**: `service/label_merge_service.py`
**方法名**: `LabelMergeService.ai_extend_perform()`

**调用的其他接口**:
1. `ai_extend_pair()` - AI扩展配对
2. `merge_and_deduplicate()` - 数据合并去重

**详细入参**:
```python
{
    "product": [                # 产品信息列表
        {
            "product_abb": str, # 产品简称
            "company_abb": str  # 公司简称
        }
    ],
    "key_companies": [          # 关键公司列表
        {
            "abb": str,         # 公司简称
            "name": str,        # 公司全名
            "is_listed": bool,  # 是否上市
            "is_special": bool, # 是否特殊公司
            "is_high_tech": bool # 是否高新技术企业
        }
    ]
}
```

**详细出参**:
```python
{
    "chain_structure": {        # 产业链结构
        "产业链名称": {
            "product": [        # 产品列表
                {
                    "product_abb": str,
                    "company_abb": str
                }
            ],
            "company": [],      # 公司列表
            "source_list": [    # 来源文档列表
                {
                    "_id": str,     # 文档ID
                    "type": str,    # 文档类型
                    "title": str,   # 文档标题
                    "file_url": str # 文件URL
                }
            ]
        }
    },
    "mongodb_id_node": dict,    # MongoDB节点映射
    "mongodb_id_info": dict     # MongoDB信息映射
}
```

### 7.3 ai_extend_pair 接口
**文件位置**: `service/label_merge_service.py`
**方法名**: `LabelMergeService.ai_extend_pair()`

**调用的其他接口**:
1. `KbService.search_knowledge_by_question()` - 知识库检索
2. `Llm_Service.answer_question()` - 大模型问答
3. `TextUtil.get_json_from_text()` - JSON解析

**详细入参**:
```python
{
    "product": str,             # 产品名称
    "company": str              # 公司名称
}
```

**详细出参**:
```python
{
    "generate_key": str,        # 生成的产业链位置
    "source_list": [            # 相关文档来源
        {
            "mongodb_id": str,      # 文档MongoDB ID
            "source_name": str,     # 来源名称
            "source_file_title": str, # 文件标题
            "source_file_url": str,   # 文件URL
            "source_content": str     # 文档内容
        }
    ]
}
```

**查询的数据表**:
1. `KNOWLEDGE_REPORT_ALL` - 知识库向量检索

## 8. 核心数据结构

### 7.1 请求参数结构 (LabelMergeRequest)
```json
{
  "mongodb_id": "标签提取返回的ID",
  "model": "大模型名称 (如: qwen2.5-72B, DeepSeek-R1-Distill-Qwen-32B)",
  "prompt": "自定义提示词",
  "merge_type": "合并类型 (Frequency/Source)",
  "is_ai_extend": "是否启用AI扩展 (true/false)",
  "is_cached": "是否使用缓存 (true/false)"
}
```

### 7.2 标签提取历史数据结构
```json
{
  "chain_structure": "产业链结构数据",
  "node_companies": "节点公司映射",
  "product": [
    {
      "product_abb": "产品简称",
      "company_abb": "公司简称"
    }
  ],
  "key_companies": "关键公司列表",
  "mongodb_id_node": "MongoDB节点ID映射",
  "mongodb_id_info": "MongoDB信息映射"
}
```

### 7.3 AI扩展生成结构
```json
{
  "产业链名称": {
    "product": [
      {
        "product_abb": "产品简称",
        "company_abb": "公司简称"
      }
    ],
    "company": "公司列表",
    "source_list": [
      {
        "_id": "文档ID",
        "type": "研报",
        "title": "文档标题",
        "file_url": "文件URL"
      }
    ]
  }
}
```

### 7.4 最终输出结构
```json
{
  "_id": "融合结果唯一ID",
  "chain_structure": "产业链结构",
  "chain_structure_json": "JSON格式产业链",
  "chain_structure_llm_merge": "LLM合并结果",
  "node_companies": "节点公司信息",
  "product": "产品信息",
  "mongodb_id_node": "节点映射",
  "mongodb_id_info": "信息映射",
  "request_param": "请求参数记录"
}
```

## 8. 数据表操作汇总

| 操作类型 | 表名 | 操作方法 | 用途 | 入参 | 出参 |
|---------|------|----------|------|------|------|
| 查询 | LABEL_MERGE_PERFORM | find_one() | 缓存查询 | 查询条件dict | cached_data/None |
| 查询 | LABEL_EXTRACT_PERFORM_HISTORY | query_doc_by_id() | 历史数据获取 | mongodb_id | history_document |
| 查询 | KNOWLEDGE_REPORT_ALL | search_by_vector() | 知识库检索 | vector, limit_top_k | doc_list |
| 插入 | LABEL_MERGE | insert_one() | 结果保存 | label_merge_info | insert_result |

## 9. 关键接口调用链

### 9.1 AI扩展调用链
```
label_merge_perform()
→ ai_extend_perform()
→ ai_extend_pair()
→ search_knowledge_by_question()
→ text_embedding()
→ search_by_vector()
→ answer_question()
```

### 9.2 标签合并调用链
```
label_merge_perform()
→ perform_common_merge_helper()
→ merge_task_multirunner_helper()
→ llm_merge() / rule_merge()
→ answer_question() / label_replace()
```

### 9.3 数据持久化调用链
```
label_merge_perform()
→ get_uuid()
→ insert_one()
→ log_json()
```

## 10. 性能优化特点

1. **智能缓存**: 基于多参数组合的缓存机制，避免重复计算
2. **异步并发**: 使用asyncio.gather并行处理多种类型标签
3. **向量检索**: Milvus快速语义相似度匹配
4. **批量处理**: 减少数据库交互次数
5. **错误恢复**: 完善的异常处理和日志记录机制

## 11. 技术栈总结

| 技术组件 | 文件位置 | 主要接口 | 功能描述 |
|---------|----------|----------|----------|
| 大模型服务 | service/llm_service.py | answer_question() | LLM问答和文本生成 |
| 向量检索 | utils/milvus_util.py | search_by_vector() | 语义相似度检索 |
| 知识库服务 | service/kb_service.py | search_knowledge_by_question() | 知识库查询 |
| 数据库操作 | utils/mongodb_util.py | find_one(), insert_one() | MongoDB数据操作 |
| 文本处理 | utils/text_utils.py | get_json_from_text() | 文本解析和处理 |
| 日志记录 | utils/log_util.py | log_json(), error() | 日志记录和错误处理 |
| 标签合并 | service/label_merge_service.py | llm_merge(), rule_merge() | 标签智能合并 |
