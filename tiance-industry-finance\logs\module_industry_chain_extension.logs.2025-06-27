[2025-06-26 16:28:35,283] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 16:28:39,438] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 16:28:39,507] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:18:34,590] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:18:38,539] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:18:38,671] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:18:53,951] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:18:57,847] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:18:57,966] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:21:12,126] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:21:16,528] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:21:16,639] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:21:16,642] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:21:20,320] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:21:20,420] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:21:27,499] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:21:31,337] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:21:31,442] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:21:56,750] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:22:00,401] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:22:00,512] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:22:00,514] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:22:04,423] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:22:04,533] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:22:04,534] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:22:08,264] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:22:08,332] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:26:09,291] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:26:13,401] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:26:13,553] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:38:34,118] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:38:38,338] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:38:38,486] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:40:14,258] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:40:18,306] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:40:18,452] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:41:16,039] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:41:20,267] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:41:20,393] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 17:59:33,520] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 17:59:37,331] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 17:59:37,508] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 18:10:39,977] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 18:10:44,542] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 18:10:44,699] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-26 18:14:58,611] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 49] INFO: 公司数据总量查询请求: {}
[2025-06-26 18:15:03,117] [module_industry_chain_extension 27044] [data_manage_service.get_company_data_statistics: 68] INFO: 公司数据统计查询成功: {'total_count': 27907, 'listed_count': 11635, 'today_updated_count': 4, 'query_date': '2025-06-26'}
[2025-06-26 18:15:03,357] [module_industry_chain_extension 27044] [data_manage_route.company_data_stats: 55] INFO: 公司数据总量查询返回结果: {"total_count": 27907, "listed_count": 11635, "today_updated_count": 4, "query_date": "2025-06-26"}
[2025-06-27 11:54:46,136] [module_industry_chain_extension 22852] [data_manage_route.company_update: 145] INFO: 公司数据批量修改请求: {"total_count": 2}
[2025-06-27 11:54:46,579] [module_industry_chain_extension 22852] [data_manage_service.get_company_info: 276] ERROR: 参数验证失败: 企业编号 COMP001 不存在或已被删除
Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 251, in get_company_info
    raise ValueError(f"企业编号 {company_code} 不存在或已被删除")
ValueError: 企业编号 COMP001 不存在或已被删除
[2025-06-27 11:54:46,640] [module_industry_chain_extension 22852] [data_manage_route.company_update: 164] INFO: 公司数据修改返回结果: {"success": false, "error_index": 0, "error_company_code": "COMP001", "error_message": "企业编号 COMP001 不存在或已被删除", "error_type": "validation_error", "total_count": 2, "processed_count": 0}
[2025-06-27 14:09:18,967] [module_industry_chain_extension 30380] [data_manage_route.company_data_stats: 72] INFO: 公司数据总量查询请求: {}
[2025-06-27 14:09:23,435] [module_industry_chain_extension 30380] [data_manage_service.get_company_data_statistics: 79] INFO: 公司数据统计查询成功: {'total_count': 27899, 'listed_count': 11636, 'today_updated_count': 1, 'query_date': '2025-06-27'}
[2025-06-27 14:09:23,542] [module_industry_chain_extension 30380] [data_manage_route.company_data_stats: 78] INFO: 公司数据总量查询返回结果: {"total_count": 27899, "listed_count": 11636, "today_updated_count": 1, "query_date": "2025-06-27"}
[2025-06-27 14:10:05,689] [module_industry_chain_extension 30380] [data_manage_route.company_update: 150] INFO: 公司数据修改请求: {"company_code": "*********0", "chi_name": "深圳瑞华泰薄膜科技股份有限公司", "chi_name_abbr": "瑞华泰", "eng_name": "Rayitek"}
[2025-06-27 14:10:05,892] [module_industry_chain_extension 30380] [data_manage_service.update_company_info: 180] INFO: 公司信息修改成功: {'company_code': '*********0', 'old_chi_name': '深圳瑞华泰薄膜科技股份有限公司', 'new_chi_name': '深圳瑞华泰薄膜科技股份有限公司', 'chi_name_abbr': '瑞华泰', 'eng_name': 'Rayitek', 'pre_name': '深圳瑞华泰薄膜科技有限公司(2004-12-17 至 2018-12-21),深圳瑞华泰薄膜科技股份有限公司', 'update_time': '2025-06-27 14:10:06', 'message': '公司信息修改成功'}
[2025-06-27 14:10:05,910] [module_industry_chain_extension 30380] [data_manage_route.company_update: 165] INFO: 公司数据修改返回结果: {"company_code": "*********0", "old_chi_name": "深圳瑞华泰薄膜科技股份有限公司", "new_chi_name": "深圳瑞华泰薄膜科技股份有限公司", "chi_name_abbr": "瑞华泰", "eng_name": "Rayitek", "pre_name": "深圳瑞华泰薄膜科技有限公司(2004-12-17 至 2018-12-21),深圳瑞华泰薄膜科技股份有限公司", "update_time": "2025-06-27 14:10:06", "message": "公司信息修改成功"}
[2025-06-27 14:10:12,315] [module_industry_chain_extension 30380] [data_manage_route.company_update: 150] INFO: 公司数据修改请求: {"company_code": "*********", "chi_name": "深圳瑞华泰薄膜科技股份有限公司", "chi_name_abbr": "瑞华泰", "eng_name": "Rayitek"}
[2025-06-27 14:10:12,327] [module_industry_chain_extension 30380] [data_manage_service.get_company_info: 276] ERROR: 参数验证失败: 企业编号 ********* 不存在或已被删除
Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 251, in get_company_info
    raise ValueError(f"企业编号 {company_code} 不存在或已被删除")
ValueError: 企业编号 ********* 不存在或已被删除
[2025-06-27 14:10:12,352] [module_industry_chain_extension 30380] [data_manage_service.update_company_info: 185] ERROR: 参数验证失败: 企业编号 ********* 不存在或已被删除
Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 251, in get_company_info
    raise ValueError(f"企业编号 {company_code} 不存在或已被删除")
ValueError: 企业编号 ********* 不存在或已被删除

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 122, in update_company_info
    CompanyUpdateService.get_company_info(company_code)
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 277, in get_company_info
    raise ValueError(error_msg)
ValueError: 企业编号 ********* 不存在或已被删除
[2025-06-27 14:10:12,356] [module_industry_chain_extension 30380] [data_manage_route.company_update: 172] ERROR: 参数验证失败：企业编号 ********* 不存在或已被删除
Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 251, in get_company_info
    raise ValueError(f"企业编号 {company_code} 不存在或已被删除")
ValueError: 企业编号 ********* 不存在或已被删除

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 122, in update_company_info
    CompanyUpdateService.get_company_info(company_code)
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 277, in get_company_info
    raise ValueError(error_msg)
ValueError: 企业编号 ********* 不存在或已被删除

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\api\routes\data_manage_route.py", line 157, in company_update
    result = CompanyUpdateService.update_company_info(
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 186, in update_company_info
    raise ValueError(error_msg)
ValueError: 企业编号 ********* 不存在或已被删除
[2025-06-27 14:11:28,252] [module_industry_chain_extension 30380] [data_manage_route.company_update: 133] INFO: 公司数据批量修改请求（列表）: {"total_count": 2}
[2025-06-27 14:11:28,629] [module_industry_chain_extension 30380] [data_manage_service.update_company_info: 180] INFO: 公司信息修改成功: {'company_code': '*********0', 'old_chi_name': '深圳瑞华泰薄膜科技股份有限公司', 'new_chi_name': '深圳瑞华泰薄膜科技股份有限公司', 'chi_name_abbr': '瑞华泰', 'eng_name': 'Rayitek', 'pre_name': '深圳瑞华泰薄膜科技有限公司(2004-12-17 至 2018-12-21),深圳瑞华泰薄膜科技股份有限公司', 'update_time': '2025-06-27 14:11:29', 'message': '公司信息修改成功'}
[2025-06-27 14:11:28,807] [module_industry_chain_extension 30380] [data_manage_service.update_company_info: 180] INFO: 公司信息修改成功: {'company_code': '*********1', 'old_chi_name': '晶瑞电子材料股份有限公司', 'new_chi_name': '晶瑞电子材料股份有限公司', 'chi_name_abbr': '晶瑞电材', 'eng_name': 'Crystal Clear Electronic Material Co., Ltd.', 'pre_name': '苏州晶瑞化学股份有限公司(2015-06-19 至 2021-07-30),苏州晶瑞化学有限公司(2001-11-29 至 2015-06-19),晶瑞电子材料股份有限公司', 'update_time': '2025-06-27 14:11:29', 'message': '公司信息修改成功'}
[2025-06-27 14:11:28,824] [module_industry_chain_extension 30380] [data_manage_route.company_update: 165] INFO: 公司数据修改返回结果: {"success": true, "total_count": 2, "updated_count": 2, "updated_companies": [{"index": 0, "company_code": "*********0", "old_chi_name": "深圳瑞华泰薄膜科技股份有限公司", "new_chi_name": "深圳瑞华泰薄膜科技股份有限公司", "update_time": "2025-06-27 14:11:29"}, {"index": 1, "company_code": "*********1", "old_chi_name": "晶瑞电子材料股份有限公司", "new_chi_name": "晶瑞电子材料股份有限公司", "update_time": "2025-06-27 14:11:29"}], "message": "批量更新成功，共更新 2 家公司"}
[2025-06-27 14:11:35,342] [module_industry_chain_extension 30380] [data_manage_route.company_update: 133] INFO: 公司数据批量修改请求（列表）: {"total_count": 2}
[2025-06-27 14:11:35,457] [module_industry_chain_extension 30380] [data_manage_service.get_company_info: 276] ERROR: 参数验证失败: 企业编号 C00000001 不存在或已被删除
Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 251, in get_company_info
    raise ValueError(f"企业编号 {company_code} 不存在或已被删除")
ValueError: 企业编号 C00000001 不存在或已被删除
[2025-06-27 14:11:35,476] [module_industry_chain_extension 30380] [data_manage_route.company_update: 165] INFO: 公司数据修改返回结果: {"success": false, "error_index": 1, "error_company_code": "C00000001", "error_message": "企业编号 C00000001 不存在或已被删除", "error_type": "validation_error", "total_count": 2, "processed_count": 1}
[2025-06-27 14:12:42,028] [module_industry_chain_extension 26824] [data_manage_route.company_update: 133] INFO: 公司数据批量修改请求（列表）: {"total_count": 2}
[2025-06-27 14:12:42,287] [module_industry_chain_extension 26824] [data_manage_service.get_company_info: 276] ERROR: 参数验证失败: 企业编号 ********* 不存在或已被删除
Traceback (most recent call last):
  File "E:\code\tiance-industry-finance\service_data_manage\service\data_manage_service.py", line 251, in get_company_info
    raise ValueError(f"企业编号 {company_code} 不存在或已被删除")
ValueError: 企业编号 ********* 不存在或已被删除
[2025-06-27 14:12:42,324] [module_industry_chain_extension 26824] [data_manage_route.company_update: 165] INFO: 公司数据修改返回结果: {"success": false, "error_index": 0, "error_company_code": "*********", "error_message": "企业编号 ********* 不存在或已被删除", "error_type": "validation_error", "total_count": 2, "processed_count": 0}
